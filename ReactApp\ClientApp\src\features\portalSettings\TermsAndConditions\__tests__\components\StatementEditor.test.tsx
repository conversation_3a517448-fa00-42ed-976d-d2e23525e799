import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import StatementEditor from "../../components/StatementEditor";
import { vi } from "vitest";
import { I18nextProvider } from "react-i18next";
import i18n from "@/utils/i18n";

describe("StatementEditor", () => {
  const setup = (value = "", onChange = vi.fn()) => {
    render(
      <I18nextProvider i18n={i18n}>
        <StatementEditor value={value} onChange={onChange} />
      </I18nextProvider>,
    );
    return { onChange };
  };

  it("renders title, textarea, and helper", () => {
    setup("Initial value");
    expect(screen.getByText(/statement_title/i)).toBeInTheDocument();
    expect(screen.getByText(/statement_helper/i)).toBeInTheDocument();
    expect(screen.getByRole("textbox")).toHaveValue("Initial value");
  });

  it("calls onChange for each keystroke with correct character", async () => {
    const user = userEvent.setup();
    const onChangeMock = vi.fn();
    setup("", onChangeMock);

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Hi");

    expect(onChangeMock).toHaveBeenCalledTimes(2);
    expect(onChangeMock.mock.calls[0][0]).toBe("H");
    expect(onChangeMock.mock.calls[1][0]).toBe("i");
  });
});
