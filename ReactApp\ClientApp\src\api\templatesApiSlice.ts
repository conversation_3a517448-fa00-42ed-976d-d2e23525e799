import {
  createApi,
  type FetchBaseQueryError,
  type QueryReturnValue,
} from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";
import type { TypeGridColumn } from "@/types/column";
import { mockTemplateDetails } from "./mocks/templatesMock";
import type {
  TemplateDetailResponse,
  TemplateListResponse,
  CreateTemplateRequest,
  CreateTemplateResponse,
  UpdateTemplateRequest,
  UpdateTemplateResponse,
} from "@/types/templates";
import { baseGridQueryParamsBuilder } from "@/utils/baseGridQueryParamsBuilder";
import appConfig from "@/config";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import httpVerbs from "@/utils/http/httpVerbs";
import config from "@/api/endpoints/endpoints";
import type { AdvancedBaseGridParams } from "@/types/advancedBaseGrid";

interface GetTemplateListSearchOptions {
  field: string;
  value: string;
}

export const templatesApiSlice = createApi({
  reducerPath: "templatesApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Template"],
  endpoints: (builder) => ({
    getTemplatesGridColumns: builder.query<TypeGridColumn[], void>({
      queryFn: async (_arg, _api, _extra, baseQuery) => {
        const result = await baseQuery({
          url: config.api[OperationalServiceTypes.PortalService]
            .getTemplatesGridColumns,
          method: httpVerbs.GET,
          headers: {
            Accept: "text/plain",
          },
          meta: OperationalServiceTypes.PortalService,
        });

        return result as QueryReturnValue<
          TypeGridColumn[],
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    getTemplateList: builder.query<
      TemplateListResponse,
      AdvancedBaseGridParams
    >({
      queryFn: async (
        { skip, take, filters, sorts },
        _api,
        _extra,
        baseQuery,
      ) => {
        const params = baseGridQueryParamsBuilder(skip, take, sorts, filters);

        const response = await baseQuery({
          url: config.api[OperationalServiceTypes.PortalService]
            .getTemplatesList,
          method: httpVerbs.GET,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: params,
          meta: OperationalServiceTypes.PortalService,
        });

        return response as QueryReturnValue<
          TemplateListResponse,
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    getTemplatesSearchOptions: builder.query<
      string[],
      GetTemplateListSearchOptions
    >({
      queryFn: async ({ field, value }, _api, _extra, baseQuery) => {
        const result = await baseQuery({
          url: config.api[
            OperationalServiceTypes.PortalService
          ].getTemplatesAutoSuggestions.replace("{field}", field),
          method: httpVerbs.GET,
          headers: {
            Accept: "application/json",
          },
          params: {
            value,
            limit: 5,
          },
          meta: OperationalServiceTypes.PortalService,
        });

        return result as QueryReturnValue<string[], FetchBaseQueryError, {}>;
      },
    }),
    downloadTemplate: builder.query<Blob, string>({
      async queryFn(templateId, _api, _extra, baseQuery) {
        const response = await baseQuery({
          url: config.api[
            OperationalServiceTypes.PortalService
          ].exportAsCSV.replace("{templateId}", templateId),
          method: httpVerbs.GET,
          headers: {
            Accept: "text/csv",
          },
          responseHandler: (res) => res.blob(),
        });
        return { data: response.data as Blob };
      },
    }),
    deleteTemplate: builder.mutation<void, string>({
      queryFn: async (templateId, _api, _extra, baseQuery) => {
        const result = await baseQuery({
          url: config.api[
            OperationalServiceTypes.PortalService
          ].deleteTemplate.replace("{templateId}", templateId),
          method: httpVerbs.DELETE,
          headers: {
            Accept: "text/plain",
          },
          meta: OperationalServiceTypes.PortalService,
        });

        return result as QueryReturnValue<void, FetchBaseQueryError, {}>;
      },
    }),
    getTemplateById: builder.query<TemplateDetailResponse, string>({
      async queryFn(
        templateId,
        _queryApi,
        _extraOptions,
        baseQuery,
      ): Promise<
        QueryReturnValue<
          TemplateDetailResponse,
          FetchBaseQueryError,
          {} | undefined
        >
      > {
        if (appConfig.featureFlags.api.GET_TEMPLATE_BY_ID) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          const templateData = mockTemplateDetails[templateId];
          if (templateData) {
            return { data: templateData };
          } else {
            return {
              error: {
                status: 404,
                data: { message: "Template not found" },
              } as FetchBaseQueryError,
            };
          }
        }

        const result = await baseQuery({
          url: config.api[
            OperationalServiceTypes.PortalService
          ].getTemplateById.replace("{templateId}", templateId),
          method: httpVerbs.GET,
          meta: OperationalServiceTypes.PortalService,
        });

        return result as QueryReturnValue<
          TemplateDetailResponse,
          FetchBaseQueryError,
          {}
        >;
      },
      providesTags: (_result, _error, templateId) => [
        { type: "Template", id: templateId },
      ],
    }),
    createTemplate: builder.mutation<
      CreateTemplateResponse,
      CreateTemplateRequest
    >({
      async queryFn(payload, _queryApi, _extraOptions, baseQuery) {
        if (appConfig.featureFlags.api.CREATE_TEMPLATE) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          return { data: { ...payload, id: "mock-id", success: true } };
        }
        const result = await baseQuery({
          url: config.api[OperationalServiceTypes.PortalService].createTemplate,
          method: httpVerbs.POST,
          body: payload,
          meta: OperationalServiceTypes.PortalService,
        });
        return result as QueryReturnValue<
          CreateTemplateResponse,
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    updateTemplate: builder.mutation<
      UpdateTemplateResponse,
      UpdateTemplateRequest
    >({
      async queryFn(payload, _queryApi, _extraOptions, baseQuery) {
        if (appConfig.featureFlags.api.UPDATE_TEMPLATE) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          return { data: { ...payload, success: true } };
        }
        const result = await baseQuery({
          url: config.api[OperationalServiceTypes.PortalService].updateTemplate,
          method: httpVerbs.PUT,
          body: payload,
          meta: OperationalServiceTypes.PortalService,
        });
        return result as QueryReturnValue<
          UpdateTemplateResponse,
          FetchBaseQueryError,
          {}
        >;
      },
      invalidatesTags: (_result, _error, arg) => [
        { type: "Template", id: arg.templateId },
      ],
    }),
  }),
});

export const {
  useGetTemplatesGridColumnsQuery,
  useGetTemplateListQuery,
  useLazyGetTemplatesSearchOptionsQuery,
  useLazyDownloadTemplateQuery,
  useDeleteTemplateMutation,
  useGetTemplateByIdQuery,
  useCreateTemplateMutation,
  useUpdateTemplateMutation,
} = templatesApiSlice;
