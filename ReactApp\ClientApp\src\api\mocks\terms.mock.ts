import type { Terms } from "@/types/terms";

export const mockTermsData: Terms = {
  termAndConditionId: 2,
  statementOfAgreement: "Custom agreement text...",
  file: {
    fileName: "terms_and_conditions_v2.pdf",
    documentUrl: "/api/Users/<USER>/2/download",
  },
  triggerPoints: {
    promptAfterFirstLogin: true,
    promptAnnually: false,
    promptQuarterly: true,
    promptWhenUpdated: true,
  },
};

// Success mock for uploadTermsPreview
export const mockUploadPreviewSuccess = {
  success: true,
  message: "Terms & Conditions uploaded successfully.",
};

// Error mock for uploadTermsPreview
export const mockUploadPreviewError = {
  success: false,
  message: "Validation error.",
  errors: {
    file: "Unsupported file format.",
  },
};

// Mock binary PDF blob (very basic valid content)
const mockPdfText = `%PDF-1.4
%âãÏÓ
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>
endobj
4 0 obj
<< /Length 55 >>
stream
BT /F1 24 Tf 100 700 Td (Hello PDF Mock) Tj ET
endstream
endobj
xref
0 5
0000000000 65535 f
0000000010 00000 n
0000000060 00000 n
0000000111 00000 n
0000000211 00000 n
trailer
<< /Root 1 0 R /Size 5 >>
startxref
310
%%EOF`;

export const mockDownloadPdfBlob = new Blob([mockPdfText], {
  type: "application/pdf",
});

export const mockUpdateTermsSuccess = {
  success: true,
  message: "Terms & Conditions updated successfully.",
};
