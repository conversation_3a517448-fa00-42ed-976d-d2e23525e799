import { useGetTemplateListQuery } from "@/api/templatesApiSlice";
import { formatDate } from "@/utils/baseGridQueryParamsBuilder";
import { useMemo } from "react";
import type { AdvancedBaseGridParams } from "@/types/advancedBaseGrid";

interface UseTemplateListProps {
  templateListParams: AdvancedBaseGridParams;
}

export const useTemplateList = ({
  templateListParams,
}: UseTemplateListProps) => {
  const { data, isLoading, isError, error, refetch, isFetching } =
    useGetTemplateListQuery(templateListParams);

  const templateList = useMemo(() => data?.records || [], [data]);
  const totalRecordCount = useMemo(() => data?.totalRecordCount || 0, [data]);

  const templateListData = useMemo(() => {
    if (!templateList) {
      return [];
    }
    return templateList.map((temp) => ({
      ...temp,
      createdOn: formatDate(temp.createdOn as string),
    }));
  }, [templateList]);

  return {
    templateListData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  };
};
