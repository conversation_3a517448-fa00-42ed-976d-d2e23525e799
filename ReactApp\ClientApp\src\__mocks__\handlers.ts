import { http, HttpResponse } from "msw";

export const handlers = [
  http.get("/api/terms-and-conditions/latest", () =>
    HttpResponse.json({
      termAndConditionId: 1,
      statementOfAgreement: "Original statement",
      triggerPoints: {
        promptAfterFirstLogin: true,
        promptWhenUpdated: true,
        promptAnnually: false,
        promptQuarterly: false,
      },
    }),
  ),

  http.post("/api/terms-and-conditions", () =>
    HttpResponse.json({ message: "Saved successfully" }),
  ),

  http.put("/api/terms-and-conditions/1", () =>
    HttpResponse.json({ message: "Updated successfully" }),
  ),

  http.post(
    "/api/terms-and-conditions/preview",
    () =>
      new HttpResponse(
        new Blob(["%PDF-1.4 fake preview"], { type: "application/pdf" }),
      ),
  ),
];
