import { useEffect } from "react";
import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";
import { useGetAuditTrailsColumns } from "./useGetAuditTrailsColumns";
import { useAuditTrailsList } from "./useAuditTrailsList";

export const useAuditTrailsController = () => {
  const { auditTrailsColumns, isLoading: isColumnsLoading } =
    useGetAuditTrailsColumns();

  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const auditTrailsListParams = {
    skip,
    take,
    filters,
    sorts,
  };

  const {
    auditTrailsData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    isFetching,
    refetch,
  } = useAuditTrailsList({
    auditTrailsListParams,
    enabled: true,
  });

  useEffect(() => {
    refetch();
  }, [skip, take, filters, sorts, refetch]);

  return {
    auditTrailsColumns,
    isColumnsLoading,
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    auditTrailsData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    isFetching,
    pagination: { skip, take },
  };
};
