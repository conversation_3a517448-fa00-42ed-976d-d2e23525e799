// React Testing Library and Vitest setup
import { render, screen, fireEvent } from "@testing-library/react";
import WrappedTermsAndConditionsPage from "@/features/portalSettings/TermsAndConditions/TermsAndConditionsPage";

// Original controller hook (we'll mock it below)
import { useTermsAndConditionsController as originalController } from "@/features/portalSettings/TermsAndConditions/hooks/useTermsAndConditionsController";
import { vi } from "vitest";

// i18n and routing setup
import { I18nextProvider } from "react-i18next";
import i18n from "@/utils/i18n";
import { MemoryRouter } from "react-router-dom";

// Enum used in triggerPoints mock
import { TRIGGER_KEYS } from "@/features/portalSettings/TermsAndConditions/constants";

beforeAll(() => {
  global.URL.createObjectURL = vi.fn(() => "mock-blob-url");
});

afterAll(() => {
  vi.restoreAllMocks();
});

// ---------- MOCKS ---------- //

// Mock the controller hook to provide controlled test data
vi.mock(
  "@/features/portalSettings/TermsAndConditions/hooks/useTermsAndConditionsController",
);

// Mock PDF viewer component to avoid loading real PDF.js and Kendo internals
vi.mock("../components/PDFPreviewPanel", () => ({
  __esModule: true,
  default: () => <div data-testid="mock-pdf-preview-panel" />,
}));

// Mock useTranslation to return keys directly (so we can test i18n safely)
vi.mock("react-i18next", async () => {
  const actual = await vi.importActual("react-i18next");
  return {
    ...actual,
    useTranslation: () => ({
      t: (key: string) => key,
      i18n: { changeLanguage: vi.fn(), language: "en" },
    }),
  };
});

// Now we use vi.mocked() to get a typed, mocked version of our controller hook
const useTermsAndConditionsController = vi.mocked(originalController);

// ---------- UTILITIES ---------- //

const mockNavigate = vi.fn();
const mockResetForm = vi.fn();
const mockHandleGeneratePreview = vi.fn();
const mockHandleSaveTerms = vi.fn();

// Mock debounce wrapper — pdf upload handler uses lodash.debounce which has cancel/flush methods
const mockDebouncedUpload = Object.assign(vi.fn(), {
  cancel: vi.fn(),
  flush: vi.fn(),
});

// This object mimics the actual return value from useTermsAndConditionsController
const defaultControllerValues = {
  terms: {
    termAndConditionId: 1,
    statementOfAgreement: "Mock agreement text",
    triggerPoints: {
      [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]: true,
      [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: false,
      [TRIGGER_KEYS.PROMPT_ANNUALLY]: true,
      [TRIGGER_KEYS.PROMPT_QUARTERLY]: false,
    },
    file: {
      fileName: "sample.pdf",
      documentUrl: "https://mock-url.com/sample.pdf",
    },
  },

  isLoading: false,
  isFetching: false,
  isSaving: false,
  errorMessage: "",
  setErrorMessage: vi.fn(),
  successMessage: "",
  setSuccessMessage: vi.fn(),
  showAlert: true,
  setShowAlert: vi.fn(),
  uploadedFileName: "uploaded.pdf",
  selectedFile: new File([""], "uploaded.pdf"),
  triggerPoints: {
    [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]: true,
    [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: false,
    [TRIGGER_KEYS.PROMPT_ANNUALLY]: true,
    [TRIGGER_KEYS.PROMPT_QUARTERLY]: false,
  },

  setTriggerPoints: vi.fn(),
  handleUpload: mockDebouncedUpload,
  handleRemoveFile: vi.fn(),
  handleSaveTerms: mockHandleSaveTerms,
  handleGeneratePreview: mockHandleGeneratePreview,
  resetForm: mockResetForm,
  navigate: mockNavigate,
  remoteBlobUrl: "",
  previewStatement: "preview",
  previewBlobUrl: "",
  statementInput: "initial statement",
  setStatementInput: vi.fn(),
  isGenerateDisabled: false,
  hasUnsavedChanges: () => true,
  defaultPreviewStatement: "default",
  uploadError: "",
  setUploadError: vi.fn(),
  setUploadedFileName: vi.fn(),
  setSelectedFile: vi.fn(),
  setPreviewBlobUrl: vi.fn(),
  setRemoteBlobUrl: vi.fn(),
  termsData: {
    termAndConditionId: 1,
    statementOfAgreement: "Mock agreement text",
    triggerPoints: {
      promptAfterFirstLogin: true,
      promptWhenUpdated: false,
      promptAnnually: true,
      promptQuarterly: false,
    },
    file: {
      fileName: "sample.pdf",
      documentUrl: "https://mock-url.com/sample.pdf",
    },
  },
  setTermsData: vi.fn(),
};

// ---------- TESTS ---------- //

describe("TermsAndConditionsPage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    useTermsAndConditionsController.mockReturnValue(defaultControllerValues);
  });

  // Helper: renders the page with all required context providers
  function renderPage() {
    return render(
      <I18nextProvider i18n={i18n}>
        <MemoryRouter>
          <WrappedTermsAndConditionsPage />
        </MemoryRouter>
      </I18nextProvider>,
    );
  }

  it("renders all key sections", () => {
    renderPage();
    // Checking if translation keys (mocked) render correctly
    expect(screen.getByText("upload_title")).toBeInTheDocument();
    expect(screen.getByText("terms_preview_title")).toBeInTheDocument();
    expect(screen.getByText("generate_preview")).toBeInTheDocument();
    expect(screen.getByText("audit_trails")).toBeInTheDocument();
    expect(screen.getByText("cancel")).toBeInTheDocument();
    expect(screen.getByText("confirm")).toBeInTheDocument();
  });

  it("displays alert box if showAlert is true", () => {
    renderPage();
    expect(screen.getByText("upload_helper_text")).toBeInTheDocument();
  });

  it("triggers handleGeneratePreview on preview button click", () => {
    renderPage();
    fireEvent.click(screen.getByText("generate_preview"));
    expect(mockHandleGeneratePreview).toHaveBeenCalled();
  });

  it("triggers handleSaveTerms on confirm click", () => {
    renderPage();
    fireEvent.click(screen.getByText("confirm"));
    expect(mockHandleSaveTerms).toHaveBeenCalled();
  });

  it("navigates to audit trails on button click", () => {
    renderPage();
    fireEvent.click(screen.getByText("audit_trails"));
    expect(mockNavigate).toHaveBeenCalled();
  });

  it("shows cancel dialog if unsaved changes exist", () => {
    renderPage();
    fireEvent.click(screen.getByText("cancel"));
    // Expect the confirmation dialog to show
    expect(screen.getByText("yes")).toBeInTheDocument();
    expect(screen.getByText("no")).toBeInTheDocument();
  });

  it("calls resetForm on confirm cancel", () => {
    renderPage();
    fireEvent.click(screen.getByText("cancel"));
    fireEvent.click(screen.getByText("yes")); // Confirm dialog
    expect(mockResetForm).toHaveBeenCalled();
  });

  it("disables confirm button when isSaving is true", () => {
    useTermsAndConditionsController.mockReturnValue({
      ...defaultControllerValues,
      isSaving: true,
    });
    renderPage();
    // Button label will change to "saving" when saving is true
    expect(screen.getByText("saving")).toBeInTheDocument();
  });

  it("disables cancel button when hasUnsavedChanges is false", () => {
    useTermsAndConditionsController.mockReturnValue({
      ...defaultControllerValues,
      hasUnsavedChanges: () => false,
    });
    renderPage();
    // Uses getByRole to target the real <button>
    expect(screen.getByRole("button", { name: "cancel" })).toBeDisabled();
  });

  it("disables preview button when isGenerateDisabled is true", () => {
    useTermsAndConditionsController.mockReturnValue({
      ...defaultControllerValues,
      isGenerateDisabled: true,
    });
    renderPage();
    expect(
      screen.getByRole("button", { name: "generate_preview" }),
    ).toBeDisabled();
  });
});
