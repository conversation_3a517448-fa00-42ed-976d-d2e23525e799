import { useMemo } from "react";
import { useGetAuditTrailsColumnsQuery } from "@/api/auditTrailsApiSlice";
import type { AuditTrailsColumn } from "@/types/auditTrails";

export const useGetAuditTrailsColumns = () => {
  const { data, isLoading } = useGetAuditTrailsColumnsQuery();

  const auditTrailsColumns: AuditTrailsColumn[] = useMemo(() => {
    return data?.filter((item) => item.key !== "acceptanceHistory") ?? [];
  }, [data]);

  return {
    auditTrailsColumns,
    isLoading,
  };
};
