import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@progress/kendo-react-buttons";
import { DropDownList } from "@progress/kendo-react-dropdowns";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import AlertBox from "@/components/AlertBox/AlertBox";
import CreateTemplateTreeView from "@/features/portalSettings/templates/components/CreateTemplateTreeView";
import { mockTemplatesData } from "@/api/mocks/templatesMock";
import { useTemplateTreeView } from "@/features/portalSettings/templates/hooks/useTemplateTreeView";
import { Loader } from "@progress/kendo-react-indicators";
import { useState } from "react";
import { filterBy } from "@progress/kendo-data-query";
import type {
  FilterDescriptor,
  CompositeFilterDescriptor,
} from "@progress/kendo-data-query";
import type { DropDownListFilterChangeEvent } from "@progress/kendo-react-dropdowns";

interface PortalTemplatesTabProps {
  showAlert: boolean;
  selectedTemplate: string | null;
  onAlertClose: () => void;
  onTemplateChange: (_e: DropDownListChangeEvent) => void;
  onClearSelection: () => void;
}

export default function PortalTemplatesTab({
  showAlert,
  selectedTemplate,
  onAlertClose,
  onTemplateChange,
  onClearSelection,
}: PortalTemplatesTabProps) {
  const { t } = useTranslation("dashboard");

  const [filteredTemplates, setFilteredTemplates] = useState(
    mockTemplatesData.records,
  );

  const filterData = (filter: FilterDescriptor | CompositeFilterDescriptor) => {
    return filterBy(mockTemplatesData.records, filter);
  };

  const handleFilterChange = (event: DropDownListFilterChangeEvent) => {
    setFilteredTemplates(filterData(event.filter));
  };

  const {
    primaryFolders,
    validation: treeValidation,
    toggleExpand,
    isLoading: isTemplateLoading,
    isError: isTemplateError,
  } = useTemplateTreeView({
    templateId: selectedTemplate || undefined,
    isEditMode: true,
  });

  return (
    <div className="portal-templates-content">
      {showAlert && (
        <AlertBox
          message={t("portalTemplates_alert_message")}
          onClose={onAlertClose}
        />
      )}

      <div className="templates-divider" />

      <div className="template-selection-row">
        <div className="template-selection-label">
          {t("portalTemplates_template_label")}
        </div>
        <div className="template-selection-dropdown">
          <DropDownList
            data={filteredTemplates}
            textField="templateName"
            dataItemKey="id"
            defaultItem={{
              templateName: t("portalTemplates_select_placeholder"),
            }}
            value={
              mockTemplatesData.records.find(
                (option) => option.id === selectedTemplate,
              ) || { templateName: t("portalTemplates_select_placeholder") }
            }
            onChange={onTemplateChange}
            filterable
            onFilterChange={handleFilterChange}
          />
        </div>
        <div className="template-selection-button">
          <Button onClick={onClearSelection} disabled={!selectedTemplate}>
            {t("portalTemplates_clear_selection")}
          </Button>
        </div>
      </div>

      <div className="templates-divider" />

      <div className="folder-structure-container">
        <div className="folder-structure-header">
          <p className="folder-structure-title">
            {t("portalTemplates_folder_structure_preview")}
          </p>
        </div>
        <div className="folder-structure-content">
          {isTemplateLoading ? (
            <div>
              <Loader size="large" />
            </div>
          ) : isTemplateError ? (
            <div>
              <p>Cannot Find Nodes for this template</p>
            </div>
          ) : selectedTemplate && primaryFolders.length > 0 ? (
            <CreateTemplateTreeView
              key={selectedTemplate}
              primaryFolders={primaryFolders}
              validation={treeValidation}
              canDeleteFolder={() => false}
              onAddPrimaryFolder={() => {}}
              onAddSecondaryFolder={() => {}}
              onEditFolder={() => {}}
              onSaveFolder={() => false}
              onCancelEdit={() => {}}
              onDeleteFolder={() => {}}
              onToggleExpand={toggleExpand}
              readOnly
            />
          ) : null}
        </div>
      </div>
    </div>
  );
}
