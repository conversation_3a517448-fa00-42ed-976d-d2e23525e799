import { describe, it, expect, vi, beforeEach } from "vitest";
import { fetchTermsPdfBlob } from "@/features/portalSettings/TermsAndConditions/utils/fetchTermsPdfBlob";

// Mocked config with feature flags
vi.mock("@/config", () => ({
  default: {
    featureFlags: {
      api: {
        DOWNLOAD_TERMS_PDF: false, // default: real API
      },
    },
  },
}));

// Mock config store with tenant and token
vi.mock("@/App", () => ({
  configStore: {
    getState: () => ({
      okta: {
        userInfo: { tenantCode: ["myTenant"] },
        idToken: "myToken",
      },
    }),
  },
}));

// apiClient mock
vi.mock("@/api/apiClient", () => ({
  default: {
    get: vi.fn(),
  },
}));

// Mock logger
vi.mock("@/utils/logger", () => ({
  default: {
    error: vi.fn(),
  },
}));

// Import after mocks
import config from "@/config";
import apiClient from "@/api/apiClient";
import logger from "@/utils/logger";

describe("fetchTermsPdfBlob", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("returns mock blob when DOWNLOAD_TERMS_PDF is true", async () => {
    // Override flag
    config.featureFlags.api.DOWNLOAD_TERMS_PDF = true;

    // Mock dynamic import of mock blob
    vi.mock("@/api/mocks/terms.mock", () => ({
      mockDownloadPdfBlob: new Blob(["mock"], { type: "application/pdf" }),
    }));

    const { mockDownloadPdfBlob } = await import("@/api/mocks/terms.mock");
    const result = await fetchTermsPdfBlob(123);

    expect(result).toEqual(mockDownloadPdfBlob);
    config.featureFlags.api.DOWNLOAD_TERMS_PDF = false; // reset for others
  });

  it("calls apiClient and returns blob when feature flag is off", async () => {
    const mockBlob = new Blob(["real"], { type: "application/pdf" });
    vi.mocked(apiClient.get).mockResolvedValueOnce({ data: mockBlob });

    const result = await fetchTermsPdfBlob(456);

    expect(result).toBe(mockBlob);
    expect(apiClient.get).toHaveBeenCalledWith(
      "myTenant/api/terms-and-conditions/456/download",
      expect.objectContaining({
        responseType: "blob",
        headers: expect.objectContaining({
          Authorization: "Bearer myToken",
        }),
      }),
    );
  });

  it("logs and throws error when apiClient.get fails", async () => {
    const error = new Error("Network failure");
    vi.mocked(apiClient.get).mockRejectedValueOnce(error);

    await expect(fetchTermsPdfBlob(789)).rejects.toThrow(
      "Unable to download PDF file.",
    );

    expect(logger.error).toHaveBeenCalledWith(
      "Failed to download PDF blob",
      error,
    );
  });
});
