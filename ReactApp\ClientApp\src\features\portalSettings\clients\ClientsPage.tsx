import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import AdvancedBaseGrid from "@/components/AdvancedBaseGrid/AdvancedBaseGrid";
import BaseGridFilterFactory from "@/components/BaseGridFilterFactory/BaseGridFilterFactory";
import GridNotice from "./components/GridNotice/GridNotice";
import AssignClientsTabStrip from "./components/AssignClients/AssignClientsTabStrip";
import { useState } from "react";
import type { TypeClient } from "@/types/clients";
import type { GridCustomCellProps } from "@progress/kendo-react-grid";
import type { ReactNode } from "react";
import { Button } from "@progress/kendo-react-buttons";
import { useTranslation } from "react-i18next";
import "./ClientsPage.scss";
import { useClientsController } from "@/features/portalSettings/clients/hooks/useClientsController";

const ClientsPage = () => {
  const { t } = useTranslation("dashboard");
  const [showNotice, setShowNotice] = useState(true);
  const [selectedClient, setSelectedClient] = useState<TypeClient | null>(null);
  const [showTabStrip, setShowTabStrip] = useState(false);

  const {
    activeTab,
    showAlert,
    selectedTemplate,
    handleTabSelect,
    handleTemplateChange,
    handleClearSelection,
    handleAlertClose,
    handleFooterConfirm,
    columns,
    isColumnsLoading,
    clientList,
    totalRecordCount,
    isLoading,
    isFetching,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    filters,
    sorts,
    pagination,
  } = useClientsController();

  const handleCloseNotice = () => {
    setShowNotice(false);
  };

  const handleRowClick = (client: TypeClient) => {
    setSelectedClient(client);
    setShowTabStrip(true);
  };

  const handleCancel = () => {
    setSelectedClient(null);
    setShowTabStrip(false);
  };

  const handleConfirm = () => {
    handleFooterConfirm();
    setSelectedClient(null);
    setShowTabStrip(false);
  };

  const getBreadcrumbTitle = (): ReactNode | undefined => {
    if (selectedClient && showTabStrip) {
      return (
        <div className="breadcrumbs custom-breadcrumbs">
          <span>DMS & Portal Manager</span>
          <span className="breadcrumb-divider">/</span>
          <span>Portal Settings</span>
          <span className="breadcrumb-divider">/</span>
          <span>Clients</span>
          <span className="breadcrumb-divider">/</span>
          <span>{selectedClient.clientName}</span>
        </div>
      );
    }
    return undefined;
  };

  const createClickableCellMapper = (): Record<
    string,
    (_props: GridCustomCellProps) => ReactNode
  > => {
    const cellMapper: Record<
      string,
      (_props: GridCustomCellProps) => ReactNode
    > = {};
    columns.forEach((column) => {
      cellMapper[column.key] = (props: GridCustomCellProps) => (
        <td
          onClick={() => handleRowClick(props.dataItem)}
          style={{ cursor: "pointer" }}
        >
          {props.dataItem[column.key]}
        </td>
      );
    });
    return cellMapper;
  };

  const dataCellMapper = createClickableCellMapper();

  return (
    <SectionLayout
      customBreadcrumb={getBreadcrumbTitle()}
      footer={
        showTabStrip ? (
          <div className="clients-footer-buttons">
            <Button onClick={handleCancel}>{t("cancel")}</Button>
            <Button
              themeColor="primary"
              onClick={handleConfirm}
              disabled={!selectedTemplate}
            >
              {t("confirm")}
            </Button>
          </div>
        ) : undefined
      }
    >
      <div className="client-page-content">
        {!showTabStrip ? (
          <>
            <GridNotice show={showNotice} onClose={handleCloseNotice} />
            <AdvancedBaseGrid
              totalRecordCount={totalRecordCount}
              columns={columns}
              dataSource={clientList}
              filters={filters}
              skip={pagination.skip}
              take={pagination.take}
              onFilterChange={handleFilterChange}
              onPageChange={handlePageChange}
              onSortChange={handleSortChange}
              onRefresh={handleRefresh}
              isLoading={isLoading || isFetching}
              sorts={sorts}
              isColumnsLoading={isColumnsLoading}
              dataCellMapper={dataCellMapper}
              renderFilterFactory={(props, column) => (
                <BaseGridFilterFactory
                  {...props}
                  column={column}
                  useAutoSuggestHook={() => ({
                    fetchSuggestions: async (
                      _field: string,
                      _value: string,
                    ) => [],
                    isLoading: false,
                  })}
                />
              )}
            />
          </>
        ) : (
          <AssignClientsTabStrip
            activeTab={activeTab}
            showAlert={showAlert}
            selectedTemplate={selectedTemplate}
            handleTabSelect={handleTabSelect}
            handleTemplateChange={handleTemplateChange}
            handleClearSelection={handleClearSelection}
            handleAlertClose={handleAlertClose}
            selectedClient={selectedClient}
          />
        )}
      </div>
    </SectionLayout>
  );
};

export default ClientsPage;
