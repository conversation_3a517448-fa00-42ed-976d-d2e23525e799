import { fireEvent, screen, render, waitFor } from "@testing-library/react";
import UploadSection from "../../components/UploadSection";
import { vi } from "vitest";
import { I18nextProvider } from "react-i18next";
import i18n from "@/utils/i18n";

describe("UploadSection", () => {
  const mockOnUpload = vi.fn();
  const mockOnRemove = vi.fn();

  const renderComponent = (props = {}) => {
    return render(
      <I18nextProvider i18n={i18n}>
        <UploadSection
          uploadedFileName=""
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
          {...props}
        />
      </I18nextProvider>,
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("displays fileNameFromTerms when uploadedFileName is empty", () => {
    renderComponent({
      uploadedFileName: "",
      fileNameFromTerms: "from-api.pdf",
    });
    expect(screen.getByDisplayValue("from-api.pdf")).toBeInTheDocument();
  });

  it("resets error and files on uploadedFileName change to empty", async () => {
    const { rerender } = render(
      <I18nextProvider i18n={i18n}>
        <UploadSection
          uploadedFileName="test.pdf"
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
        />
      </I18nextProvider>,
    );

    rerender(
      <I18nextProvider i18n={i18n}>
        <UploadSection
          uploadedFileName=""
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
        />
      </I18nextProvider>,
    );

    // error and file state reset, nothing visible
    expect(screen.getByPlaceholderText("upload_placeholder")).toHaveValue("");
  });

  it("uploads valid file and clears error", async () => {
    renderComponent();

    const validFile = {
      name: "ok.pdf",
      validationErrors: [],
    };

    const event = {
      affectedFiles: [validFile],
      newState: [validFile],
    };

    mockOnUpload(event);
    fireEvent(
      screen.getByPlaceholderText("upload_placeholder"),
      new Event("change"),
    );

    await waitFor(() => {
      expect(screen.queryByRole("alert")).not.toBeInTheDocument();
    });
  });
});
