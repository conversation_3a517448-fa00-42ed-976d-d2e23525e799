import { renderHook, act } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { I18nextProvider } from "react-i18next";
import { Provider } from "react-redux";
import { vi } from "vitest";
import i18n from "@/utils/i18n";
import { store } from "@/store/store";
import { TRIGGER_KEYS } from "@/features/portalSettings/TermsAndConditions/constants";
import { useTermsAndConditionsController } from "@/features/portalSettings/TermsAndConditions/hooks/useTermsAndConditionsController";
import * as getHook from "@/features/portalSettings/TermsAndConditions/hooks/useGetLatest";
import * as saveHook from "@/features/portalSettings/TermsAndConditions/hooks/useSaveTerms";
import * as updateHook from "@/features/portalSettings/TermsAndConditions/hooks/useUpdateTerms";
import logger from "@/utils/logger";

beforeAll(() => {
  vi.stubGlobal("URL", {
    createObjectURL: vi.fn(() => "mocked-blob-url"),
    revokeObjectURL: vi.fn(),
  });
});

vi.useFakeTimers();

export function getWrapperWithProviders(initialEntries: string[] = ["/"]) {
  const Component = ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>
      <MemoryRouter initialEntries={initialEntries}>{children}</MemoryRouter>
    </Provider>
  );
  Component.displayName = "MemoryRouterWrapper";
  return Component;
}

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>
    <MemoryRouter>
      <I18nextProvider i18n={i18n}>{children}</I18nextProvider>
    </MemoryRouter>
  </Provider>
);

wrapper.displayName = "TestWrapper";

describe("useTermsAndConditionsController", () => {
  it("initializes state from useGetLatest", async () => {
    const mockTerms = {
      termAndConditionId: 42,
      statementOfAgreement: "Initial statement",
      triggerPoints: {
        promptAfterFirstLogin: true,
        promptWhenUpdated: false,
        promptAnnually: true,
        promptQuarterly: false,
      },
      file: {
        fileName: "terms.pdf",
        documentUrl: "https://example.com/terms.pdf",
      },
    };

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
      error: undefined,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    expect(result.current.terms).toEqual(mockTerms);
    expect(result.current.statementInput).toBe("Initial statement");
    expect(result.current.defaultPreviewStatement).toBe("Initial statement");
    expect(result.current.triggerPoints).toEqual({
      [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]: true,
      [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: false,
      [TRIGGER_KEYS.PROMPT_ANNUALLY]: true,
      [TRIGGER_KEYS.PROMPT_QUARTERLY]: false,
    });
  });

  it("handles valid file upload and sets selectedFile", async () => {
    const file = new File(["dummy content"], "terms.pdf", {
      type: "application/pdf",
    });

    const mockEvent = {
      affectedFiles: [
        {
          name: "terms.pdf",
          getRawFile: () => file,
          validationErrors: [],
        },
      ],
    };

    const mockTerms = {
      termAndConditionId: 1,
      statementOfAgreement: "",
      triggerPoints: {
        promptAfterFirstLogin: false,
        promptWhenUpdated: false,
        promptAnnually: false,
        promptQuarterly: false,
      },
      file: {
        fileName: "terms.pdf",
        documentUrl: "https://example.com/terms.pdf",
      },
    };

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
      error: undefined,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    act(() => {
      result.current.handleUpload(mockEvent);
    });

    await act(() => vi.runAllTimersAsync());

    expect(result.current.selectedFile).toBe(file);
    expect(result.current.uploadedFileName).toBe("terms.pdf");
    expect(result.current.uploadError).toBe("");
    expect(result.current.errorMessage).toBe("");
  });

  it("handles invalid file upload and sets uploadError", async () => {
    const file = new File(["dummy content"], "invalid.pdf", {
      type: "application/pdf",
    });

    const mockEvent = {
      affectedFiles: [
        {
          name: "invalid.pdf",
          getRawFile: () => file,
          validationErrors: [{ message: "File too large" }],
        },
      ],
    };

    const loggerWarnSpy = vi
      .spyOn(console, "warn")
      .mockImplementation(() => {});
    const loggerErrorSpy = vi
      .spyOn(console, "error")
      .mockImplementation(() => {});

    const mockTerms = {
      termAndConditionId: 1,
      statementOfAgreement: "",
      triggerPoints: {
        promptAfterFirstLogin: false,
        promptWhenUpdated: false,
        promptAnnually: false,
        promptQuarterly: false,
      },
      file: {
        fileName: "existing.pdf",
        documentUrl: "https://example.com/existing.pdf",
      },
    };

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
      error: undefined,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    act(() => {
      result.current.handleUpload(mockEvent);
    });

    await act(() => vi.runAllTimersAsync());

    expect(result.current.uploadedFileName).toBe("invalid.pdf");
    expect(result.current.selectedFile).toBeNull();
    expect(result.current.uploadError).toBe("file_validation_error");

    loggerWarnSpy.mockRestore();
    loggerErrorSpy.mockRestore();
  });

  test("generates preview with selectedFile and sets preview data", async () => {
    const loggerInfoSpy = vi.spyOn(logger, "info").mockImplementation(() => {});

    const mockFile = new File(["pdf content"], "preview.pdf", {
      type: "application/pdf",
    });
    Object.setPrototypeOf(mockFile, Blob.prototype); // ✅ ensure it's treated as a Blob

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper: getWrapperWithProviders(),
    });

    act(() => {
      result.current.setStatementInput("Preview Statement");
      result.current.setSelectedFile(mockFile);
    });

    await act(async () => {
      await result.current.handleGeneratePreview();
    });

    expect(loggerInfoSpy).toHaveBeenCalledWith(
      "Generating preview with:",
      expect.objectContaining({
        file: mockFile,
        statementOfAgreement: "Preview Statement",
      }),
    );

    loggerInfoSpy.mockRestore();
  });

  it("calls saveTerms when file and preview exist, shows success and resets form", async () => {
    const mockFile = new File(["test"], "upload.pdf", {
      type: "application/pdf",
    });

    const mockRefetch = vi.fn().mockResolvedValue({
      data: {
        statementOfAgreement: "Refetched",
        triggerPoints: {
          promptAfterFirstLogin: false,
          promptWhenUpdated: false,
          promptAnnually: false,
          promptQuarterly: false,
        },
        file: {
          fileName: "refetch.pdf",
          documentUrl: "https://example.com/refetch.pdf",
        },
      },
    });

    const saveSpy = vi.fn((_payload, _onError, onSuccess) => {
      onSuccess?.(); // Simulate success callback
      return Promise.resolve();
    });

    const updateSpy = vi.fn(); // Should NOT be called

    const mockTerms = {
      termAndConditionId: 99,
      statementOfAgreement: "Original",
      triggerPoints: {
        promptAfterFirstLogin: false,
        promptWhenUpdated: false,
        promptAnnually: false,
        promptQuarterly: false,
      },
      file: {
        fileName: "original.pdf",
        documentUrl: "https://example.com/original.pdf",
      },
    };

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: mockRefetch,
      error: undefined,
    });

    vi.spyOn(saveHook, "useSaveTerms").mockReturnValue({
      save: saveSpy,
      isSaving: false,
    });

    vi.spyOn(updateHook, "useUpdateTerms").mockReturnValue({
      update: updateSpy,
      isUpdating: false,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    // Set up state: selected file, modified statement, preview blob
    act(() => {
      result.current.setSelectedFile(mockFile);
      result.current.setStatementInput("Updated Agreement");
    });

    await act(async () => {
      await result.current.handleGeneratePreview();
    });

    await act(async () => {
      await result.current.handleSaveTerms();
    });

    expect(saveSpy).toHaveBeenCalledWith(
      {
        termAndConditionId: 99,
        statementOfAgreement: "Updated Agreement",
        triggerPoints: {
          promptAfterFirstLogin: true,
          promptWhenUpdated: false,
          promptAnnually: false,
          promptQuarterly: false,
        },
        file: mockFile,
      },
      expect.any(Function),
      expect.any(Function),
    );

    expect(result.current.successMessage).toBe("terms_saved_successfully"); // raw translation key unless mocked
    expect(mockRefetch).toHaveBeenCalled();
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it("calls updateTerms when statement or trigger points change (no file)", async () => {
    const updateSpy = vi.fn((_payload, _onError, onSuccess) => {
      onSuccess?.("Updated successfully");
      return Promise.resolve();
    });

    const saveSpy = vi.fn(); // should not be called

    const mockTerms = {
      termAndConditionId: 100,
      statementOfAgreement: "Original agreement",
      triggerPoints: {
        promptAfterFirstLogin: true,
        promptWhenUpdated: false,
        promptAnnually: false,
        promptQuarterly: false,
      },
      file: {
        fileName: "terms.pdf",
        documentUrl: "https://example.com/terms.pdf",
      },
    };

    const mockRefetch = vi.fn().mockResolvedValue({
      data: mockTerms,
    });

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: mockRefetch,
      error: undefined,
    });

    vi.spyOn(updateHook, "useUpdateTerms").mockReturnValue({
      update: updateSpy,
      isUpdating: false,
    });

    vi.spyOn(saveHook, "useSaveTerms").mockReturnValue({
      save: saveSpy,
      isSaving: false,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    // Change the statement and a trigger point
    act(() => {
      result.current.setStatementInput("Modified agreement");
      result.current.setTriggerPoints({
        ...result.current.triggerPoints,
        promptQuarterly: true,
      });
    });

    await act(async () => {
      await result.current.handleSaveTerms();
    });

    expect(updateSpy).toHaveBeenCalledWith(
      {
        termAndConditionId: 100,
        statementOfAgreement: "Modified agreement",
        triggerPoints: {
          promptAfterFirstLogin: true,
          promptWhenUpdated: false,
          promptAnnually: false,
          promptQuarterly: true,
        },
      },
      expect.any(Function),
      expect.any(Function),
    );

    expect(saveSpy).not.toHaveBeenCalled();
    expect(result.current.successMessage).toBe("terms_updated_successfully"); // may need i18n mock
  });

  it("shows no_changes_to_save error when there are no file or input changes", async () => {
    const updateSpy = vi.fn();
    const saveSpy = vi.fn();

    const mockTerms = {
      termAndConditionId: 200,
      statementOfAgreement: "No changes made",
      triggerPoints: {
        promptAfterFirstLogin: true,
        promptWhenUpdated: false,
        promptAnnually: false,
        promptQuarterly: false,
      },
      file: {
        fileName: "current.pdf",
        documentUrl: "https://example.com/current.pdf",
      },
    };

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
      error: undefined,
    });

    vi.spyOn(updateHook, "useUpdateTerms").mockReturnValue({
      update: updateSpy,
      isUpdating: false,
    });

    vi.spyOn(saveHook, "useSaveTerms").mockReturnValue({
      save: saveSpy,
      isSaving: false,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    // No changes to statementInput, triggerPoints, or file
    act(() => {
      result.current.setStatementInput("No changes made"); // same as original
    });

    await act(async () => {
      await result.current.handleSaveTerms();
    });

    expect(updateSpy).not.toHaveBeenCalled();
    expect(saveSpy).not.toHaveBeenCalled();
    expect(result.current.errorMessage).toBe("no_changes_to_save"); // default i18n key
  });

  it("resets form correctly after calling resetForm", async () => {
    const mockRefetchedTerms = {
      statementOfAgreement: "Reset statement",
      triggerPoints: {
        promptAfterFirstLogin: false,
        promptWhenUpdated: true,
        promptAnnually: true,
        promptQuarterly: false,
      },
      file: {
        fileName: "reset.pdf",
        documentUrl: "https://example.com/reset.pdf",
      },
    };

    const refetchSpy = vi.fn().mockResolvedValue({ data: mockRefetchedTerms });

    const mockTerms = {
      termAndConditionId: 123,
      statementOfAgreement: "Original statement",
      triggerPoints: {
        promptAfterFirstLogin: true,
        promptWhenUpdated: false,
        promptAnnually: false,
        promptQuarterly: true,
      },
      file: {
        fileName: "original.pdf",
        documentUrl: "https://example.com/original.pdf",
      },
    };

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: refetchSpy,
      error: undefined,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    // Mutate some state
    act(() => {
      result.current.setStatementInput("Changed input");
      result.current.setUploadedFileName("changed.pdf");
      result.current.setUploadError("File too large");
      result.current.setSelectedFile(
        new File(["fake"], "changed.pdf", { type: "application/pdf" }),
      );
      result.current.setErrorMessage("Some error");
      result.current.setSuccessMessage("Some success");
    });

    // Call resetForm
    await act(async () => {
      await result.current.resetForm();
    });

    expect(refetchSpy).toHaveBeenCalled();
    expect(result.current.statementInput).toBe("Reset statement");
    expect(result.current.defaultPreviewStatement).toBe("Reset statement");
    expect(result.current.triggerPoints).toEqual({
      promptAfterFirstLogin: true,
      promptWhenUpdated: true,
      promptAnnually: true,
      promptQuarterly: false,
    });
    expect(result.current.selectedFile).toBe(null);
    expect(result.current.uploadedFileName).toBe("");
    expect(result.current.previewBlobUrl).toBe(null);
    expect(result.current.errorMessage).toBe("");
    expect(result.current.uploadError).toBe("");
    expect(result.current.successMessage).toBe("Some success"); // resetForm does not clear this
  });

  it("handleRemoveFile clears file-related state", () => {
    const mockTerms = {
      termAndConditionId: 1,
      statementOfAgreement: "Agreement",
      triggerPoints: {
        promptAfterFirstLogin: false,
        promptWhenUpdated: false,
        promptAnnually: false,
        promptQuarterly: false,
      },
      file: {
        fileName: "existing.pdf",
        documentUrl: "https://example.com/existing.pdf",
      },
    };

    vi.spyOn(getHook, "useGetLatest").mockReturnValue({
      terms: mockTerms,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
      error: undefined,
    });

    const { result } = renderHook(() => useTermsAndConditionsController(), {
      wrapper,
    });

    // Set state before removing file
    act(() => {
      result.current.setUploadedFileName("file-to-remove.pdf");
      result.current.setSelectedFile(new File(["test"], "file-to-remove.pdf"));
      result.current.setUploadError("Validation error");
    });

    act(() => {
      result.current.handleRemoveFile();
    });

    expect(result.current.uploadedFileName).toBe("");
    expect(result.current.selectedFile).toBeNull();
    expect(result.current.uploadError).toBe("");
  });
});
