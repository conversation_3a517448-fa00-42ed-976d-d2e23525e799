import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ConditionsForm from "../../components/ConditionsForm";
import { TRIGGER_KEYS, DISPLAY_FREQUENCIES } from "../../constants";
import { vi } from "vitest";

// Default props for testing
const defaultValues = {
  [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]: true,
  [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: false,
  [TRIGGER_KEYS.PROMPT_ANNUALLY]: true,
  [TRIGGER_KEYS.PROMPT_QUARTERLY]: false,
};

describe("ConditionsForm", () => {
  const mockOnChange = vi.fn();

  const setup = (customValues = defaultValues) => {
    render(<ConditionsForm values={customValues} onChange={mockOnChange} />);
  };

  it("renders all inputs with correct initial state", () => {
    setup();

    // Labels
    expect(screen.getByText("title_display_conditions")).toBeInTheDocument();
    expect(screen.getByText("sub_text")).toBeInTheDocument();

    // First login checkbox (disabled)
    const firstLoginCheckbox = screen.getByLabelText(
      "first_login",
    ) as HTMLInputElement;
    expect(firstLoginCheckbox).toBeInTheDocument();
    expect(firstLoginCheckbox.checked).toBe(true);
    expect(firstLoginCheckbox.disabled).toBe(true);

    // Updated checkbox
    const updatedCheckbox = screen.getByLabelText(
      "updated",
    ) as HTMLInputElement;
    expect(updatedCheckbox).toBeInTheDocument();
    expect(updatedCheckbox.checked).toBe(false);

    // Annually radio
    const annuallyRadio = screen.getByDisplayValue(
      DISPLAY_FREQUENCIES.ANNUALLY,
    ) as HTMLInputElement;
    expect(annuallyRadio.checked).toBe(true);

    // Quarterly radio
    const quarterlyRadio = screen.getByDisplayValue(
      DISPLAY_FREQUENCIES.QUARTERLY,
    ) as HTMLInputElement;
    expect(quarterlyRadio.checked).toBe(false);
  });

  it("calls onChange when updated checkbox is clicked", async () => {
    setup();
    const updatedCheckbox = screen.getByLabelText("updated");
    await userEvent.click(updatedCheckbox);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultValues,
      [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: true,
    });
  });

  it("calls onChange when annually radio is clicked", async () => {
    setup({
      ...defaultValues,
      [TRIGGER_KEYS.PROMPT_ANNUALLY]: false,
      [TRIGGER_KEYS.PROMPT_QUARTERLY]: true,
    });

    const annuallyRadio = screen.getByDisplayValue(
      DISPLAY_FREQUENCIES.ANNUALLY,
    );
    await userEvent.click(annuallyRadio);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultValues,
      [TRIGGER_KEYS.PROMPT_ANNUALLY]: true,
      [TRIGGER_KEYS.PROMPT_QUARTERLY]: false,
    });
  });

  it("calls onChange when quarterly radio is clicked", async () => {
    setup();

    const quarterlyRadio = screen.getByDisplayValue(
      DISPLAY_FREQUENCIES.QUARTERLY,
    );
    await userEvent.click(quarterlyRadio);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultValues,
      [TRIGGER_KEYS.PROMPT_ANNUALLY]: false,
      [TRIGGER_KEYS.PROMPT_QUARTERLY]: true,
    });
  });
});
