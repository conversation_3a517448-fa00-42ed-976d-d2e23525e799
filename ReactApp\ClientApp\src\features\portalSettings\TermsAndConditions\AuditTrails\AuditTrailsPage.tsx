import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { ExportCsvFile } from "./components";
import AdvancedBaseGrid from "@/components/AdvancedBaseGrid/AdvancedBaseGrid";
import { useAuditTrailsAutoSuggest } from "./hooks/useAuditTrailsAutoSuggest";
import BaseGridFilterFactory from "@/components/BaseGridFilterFactory/BaseGridFilterFactory";
import "./AuditTrailsPage.scss";
import { useAuditTrailsController } from "./hooks/useAuditTrailsController";

export default function AuditTrailsPage() {
  const {
    auditTrailsData,
    totalRecordCount,
    auditTrailsColumns,
    isLoading,
    isColumnsLoading,
    pagination,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    isFetching,
  } = useAuditTrailsController();

  return (
    <SectionLayout>
      <AdvancedBaseGrid
        totalRecordCount={totalRecordCount}
        columns={auditTrailsColumns}
        dataSource={auditTrailsData}
        filters={filters}
        skip={pagination.skip}
        take={pagination.take}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
        onRefresh={handleRefresh}
        isLoading={isLoading || isFetching}
        sorts={sorts}
        isColumnsLoading={isColumnsLoading}
        actionsColumn={{
          label: "Acceptance History",
          renderer: (props) => <ExportCsvFile {...props} />,
        }}
        renderFilterFactory={(props, column) => (
          <BaseGridFilterFactory
            {...props}
            column={column}
            useAutoSuggestHook={useAuditTrailsAutoSuggest}
            dataRangeMode
          />
        )}
      />
    </SectionLayout>
  );
}
