import { TrashIcon, PencilSquareIcon } from "@heroicons/react/24/outline";
import "../TemplatesPage.scss";

interface TemplatesGridActionsProps {
  onEdit: () => void;
  onDelete: () => void;
}

const TemplatesGridActions = ({
  onEdit,
  onDelete,
}: TemplatesGridActionsProps) => {
  return (
    <td className="actions-cell">
      <PencilSquareIcon
        onClick={onEdit}
        type="button"
        height={20}
        width={20}
        color="#007acc"
        cursor={"pointer"}
      />
      <TrashIcon
        onClick={onDelete}
        type="button"
        height={20}
        width={20}
        color="#007acc"
        cursor={"pointer"}
      />
    </td>
  );
};

export default TemplatesGridActions;
