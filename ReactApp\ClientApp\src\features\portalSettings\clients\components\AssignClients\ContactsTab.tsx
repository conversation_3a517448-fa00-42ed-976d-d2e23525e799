import type { TypeClient } from "@/types/clients";

interface ContactsTabProps {
  selectedClient?: TypeClient | null; // Client data passed from the grid
}

export default function ContactsTab({ selectedClient }: ContactsTabProps) {
  return (
    <div className="contacts-content">
      {selectedClient && (
        <div className="client-info">
          <h4>Client: {selectedClient.clientName}</h4>
        </div>
      )}
      <p>Contacts tab content - placeholder</p>
    </div>
  );
}
