import { render, screen, cleanup } from "@testing-library/react";
import PDFPreviewPanel from "../../components/PDFPreviewPanel";
import { vi } from "vitest";
import i18n from "@/utils/i18n";
import { I18nextProvider } from "react-i18next";

vi.mock("@progress/kendo-react-pdf-viewer", () => ({
  PDFViewer: () => <div role="region">Mock PDFViewer</div>,
}));

const renderWithI18n = (ui: React.ReactNode) =>
  render(<I18nextProvider i18n={i18n}>{ui}</I18nextProvider>);

describe("PDFPreviewPanel", () => {
  afterEach(() => {
    cleanup();
    vi.restoreAllMocks();
  });

  it("renders fallback text when no URL is provided", () => {
    renderWithI18n(
      <PDFPreviewPanel
        remoteBlobUrl={null}
        previewBlobUrl={null}
        isSaving={false}
        file={null}
        terms={{}}
        previewStatement={null}
        initialStatement=""
      />,
    );

    expect(screen.getByText("no_preview")).toBeInTheDocument();
    expect(screen.getByText("preview_text")).toBeInTheDocument();
  });

  it("renders previewStatement if available", () => {
    renderWithI18n(
      <PDFPreviewPanel
        remoteBlobUrl={null}
        previewBlobUrl={null}
        isSaving={false}
        file={null}
        terms={{}}
        previewStatement="This is a preview statement."
        initialStatement="Initial statement"
      />,
    );

    expect(
      screen.getByText("This is a preview statement."),
    ).toBeInTheDocument();
  });

  it("falls back to initialStatement when previewStatement is empty", () => {
    renderWithI18n(
      <PDFPreviewPanel
        remoteBlobUrl={null}
        previewBlobUrl={null}
        isSaving={false}
        file={null}
        terms={{}}
        previewStatement=""
        initialStatement="Initial statement"
      />,
    );

    expect(screen.getByText("Initial statement")).toBeInTheDocument();
  });

  it("renders PDFViewer when remoteBlobUrl is provided", () => {
    renderWithI18n(
      <PDFPreviewPanel
        remoteBlobUrl="https://example.com/sample.pdf"
        previewBlobUrl={null}
        isSaving={false}
        file={null}
        terms={{}}
        previewStatement=""
        initialStatement=""
      />,
    );

    expect(screen.getByRole("region")).toBeInTheDocument(); // PDFViewer renders a div with role="region"
  });

  it("calls URL.revokeObjectURL on unmount if previewBlobUrl exists", () => {
    const mockRevoke = vi.fn();
    global.URL.revokeObjectURL = mockRevoke;

    const previewUrl = "blob:http://localhost/mock";

    const { unmount } = renderWithI18n(
      <PDFPreviewPanel
        remoteBlobUrl={null}
        previewBlobUrl={previewUrl}
        isSaving={false}
        file={null}
        terms={{}}
        previewStatement=""
        initialStatement=""
      />,
    );

    unmount();
    expect(mockRevoke).toHaveBeenCalledWith(previewUrl);
  });

  it("renders disabled buttons and icons in footer", () => {
    renderWithI18n(
      <PDFPreviewPanel
        remoteBlobUrl={null}
        previewBlobUrl={null}
        isSaving={false}
        file={null}
        terms={{}}
        previewStatement=""
        initialStatement=""
      />,
    );

    expect(screen.getByText("refresh")).toBeInTheDocument();
    expect(screen.getByText("download")).toBeInTheDocument();
    expect(screen.getByText("print")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /agree/i })).toBeDisabled();
  });
});
