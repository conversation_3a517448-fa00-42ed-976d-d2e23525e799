import { useTranslation } from "react-i18next";
import {
  TabStrip,
  TabStripTab,
  type TabStripSelectEventArguments,
} from "@progress/kendo-react-layout";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import type { TypeClient } from "@/types/clients";
import PortalTemplatesTab from "./PortalTemplatesTab";
import ContactsTab from "./ContactsTab";

interface AssignClientsTabStripProps {
  activeTab: number;
  showAlert: boolean;
  selectedTemplate: string | null;
  handleTabSelect: (_e: TabStripSelectEventArguments) => void;
  handleTemplateChange: (_e: DropDownListChangeEvent) => void;
  handleClearSelection: () => void;
  handleAlertClose: () => void;
  selectedClient?: TypeClient | null;
}

export default function AssignClientsTabStrip({
  activeTab,
  showAlert,
  selectedTemplate,
  handleTabSelect,
  handleTemplateChange,
  handleClearSelection,
  handleAlertClose,
  selectedClient,
}: AssignClientsTabStripProps) {
  const { t } = useTranslation("dashboard");

  return (
    <TabStrip selected={activeTab} onSelect={handleTabSelect}>
      <TabStripTab title={t("AssignClients_tab_title")}>
        <PortalTemplatesTab
          showAlert={showAlert}
          selectedTemplate={selectedTemplate}
          onAlertClose={handleAlertClose}
          onTemplateChange={handleTemplateChange}
          onClearSelection={handleClearSelection}
        />
      </TabStripTab>
      <TabStripTab title={t("AssignClients_contacts_tab_title")}>
        <ContactsTab selectedClient={selectedClient} />
      </TabStripTab>
    </TabStrip>
  );
}
