/* Sidebar Container */
.sidebar-container {
  width: 220px;
  padding: 0.75rem 1rem;
  background-color: var(--kendo-color-bg, #fff);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 1px;
  border: 1px solid var(--kendo-color-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Scrollbar Styling */
.sidebar-container::-webkit-scrollbar {
  width: 6px;
}
.sidebar-container::-webkit-scrollbar-track {
  background: transparent;
}
.sidebar-container::-webkit-scrollbar-thumb {
  background-color: var(--kendo-color-subtle);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
}

/* Search Wrapper */
.search-wrapper {
  position: relative;
  margin-bottom: 0.75rem;
}
.search-input {
  height: 2rem;
  font-size: 0.875rem;
  padding-right: 2.5rem;
  background-color: var(--kendo-color-bg, #fff);
  border: 1px solid var(--kendo-color-border);
  border-radius: 0.15rem;
  width: 100%;
}
.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  fill: var(--kendo-subtle-text);
  pointer-events: none;
}

/* PanelBar Styling */
.k-panelbar {
  width: 100%;
  overflow: hidden;
  border: none !important;
  box-shadow: none !important;
  background: none !important;
}
.k-panelbar .k-panelbar-item,
.k-panelbar .k-panelbar-header {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  width: 100%;
}

/* Hide only Kendo's new SVG caret */
.custom-panelbar-item .k-panelbar-toggle.k-panelbar-expand,
.custom-panelbar-item .k-panelbar-toggle.k-panelbar-collapse {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  visibility: hidden !important;
}

/* Menu Title Layout */
.menu-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8125rem;
  font-weight: 600;
  padding: 0.6rem 0;
  cursor: pointer;
  border-bottom: 1px solid var(--kendo-color-border);
}

/* Custom caret icon */
.caret-placeholder {
  width: 18px;
  height: 18px;
  fill: var(--kendo-subtle-text);
  transition: transform 0.2s ease;
}
.menu-title.expanded .caret-placeholder {
  transform: rotate(180deg);
}

/* Icon + Text */
.menu-heroicon {
  width: 18px;
  height: 18px;
  color: var(--kendo-body-text);
  flex-shrink: 0;
}
.menu-text {
  color: var(--kendo-body-text);
  font-size: 0.8125rem;
  font-weight: 600;
}

/* Count right aligned */
.menu-count {
  font-size: 0.75rem;
  color: var(--kendo-color-primary);
  font-weight: 600;
  margin-left: auto;
}

/* Submenu Styling */
.k-panelbar-item {
  margin-bottom: 0.1rem;
}
.submenu-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.78125rem;
  color: var(--kendo-body-text);
  padding: 0.2rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  margin-left: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background-color 0.2s, color 0.2s;
}
.submenu-item:hover {
  background-color: var(--kendo-hover-bg, rgba(0, 0, 0, 0.04));
}
.submenu-item.active {
  background-color: rgba(0, 123, 255, 0.12);
  color: var(--kendo-color-primary);
  font-weight: 600;
}
.submenu-item.active .submenu-heroicon {
  color: var(--kendo-color-primary);
}
.submenu-heroicon {
  width: 18px;
  height: 18px;
  color: var(--kendo-body-text);
  flex-shrink: 0;
}

/* Divider */
.sidebar-divider {
  width: calc(100% + 2rem);
  height: 1px;
  background: var(--kendo-color-border);
  margin: 0 -1rem;
  border: none;
  display: block;
}

/* Scroll area */
.sidebar-menu-scroll {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
}

.submenu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  color: #333;

  .submenu-heroicon {
    color: #666;
  }

  &.active {
    background-color: #1B69B9; // Kendo blue
    color: #fff;

    .submenu-heroicon {
      color: #fff;
    }

    // Prevent hover effect when active
    &:hover {
      background-color: #1B69B9;
    }
  }

  // Hover only applies to non-active items
  &:not(.active):hover {
    background-color: #f0f0f0;
  }
}
