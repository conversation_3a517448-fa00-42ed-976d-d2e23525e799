import { useEffect, useState, type ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { Card } from "@progress/kendo-react-layout";
import { Loader } from "@progress/kendo-react-indicators";
import {
  Notification,
  NotificationGroup,
} from "@progress/kendo-react-notification";
import "./SectionLayout.scss";
import { menuData } from "@/constants/menuData";
import type { MenuItem } from "@/types";

interface SectionLayoutProps {
  isLoading?: boolean;
  isFetching?: boolean;
  isSaving?: boolean;
  errorMessage?: string;
  onCloseError?: () => void;
  successMessage?: string;
  onCloseSuccess?: () => void;
  headerActions?: ReactNode;
  children: ReactNode;
  footer?: ReactNode;
  customBreadcrumb?: ReactNode;
}

// Recursive function to match menu route and return breadcrumb trail
function findBreadcrumbTrail(
  items: MenuItem[],
  pathname: string,
  trail: MenuItem[] = [],
): MenuItem[] | null {
  for (const item of items) {
    const currentTrail = [...trail, item];

    if (item.route === pathname) {
      return currentTrail;
    }

    if (pathname.startsWith(item.route || "") && item.children) {
      const childTrail = findBreadcrumbTrail(
        item.children,
        pathname,
        currentTrail,
      );
      if (childTrail) return childTrail;
    }
  }

  return null;
}

export default function SectionLayout({
  isLoading = false,
  isFetching = false,
  isSaving = false,
  errorMessage = "",
  onCloseError,
  successMessage = "",
  onCloseSuccess,
  headerActions,
  children,
  footer,
  customBreadcrumb,
}: SectionLayoutProps) {
  const { t } = useTranslation("dashboard");
  const location = useLocation();

  const path = location.pathname;
  const isDashboard = path === "/dashboard";

  const breadcrumbTrail = findBreadcrumbTrail(menuData, path) ?? [];
  const breadcrumbKeys = [
    t("portal"),
    ...breadcrumbTrail.map((item) => item.title),
  ];

  const showLoader = isLoading || isFetching || isSaving;
  const [showError, setShowError] = useState(!!errorMessage);
  const [showSuccess, setShowSuccess] = useState(!!successMessage);

  useEffect(() => {
    setShowError(!!errorMessage);
    if (errorMessage) {
      const timer = setTimeout(() => {
        setShowError(false);
        onCloseError?.();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage, onCloseError]);

  useEffect(() => {
    setShowSuccess(!!successMessage);
    if (successMessage) {
      const timer = setTimeout(() => {
        setShowSuccess(false);
        onCloseSuccess?.();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, onCloseSuccess]);

  return (
    <div className="dashboard-page">
      {showLoader && (
        <div className="loading-overlay">
          <Loader size="large" type="infinite-spinner" themeColor="primary" />
        </div>
      )}

      {showError && (
        <NotificationGroup style={{ right: 10, top: 10 }}>
          <Notification
            className="fade-notification"
            type={{ style: "error", icon: true }}
            closable={true}
            onClose={() => {
              setShowError(false);
              onCloseError?.();
            }}
          >
            <span>{errorMessage}</span>
          </Notification>
        </NotificationGroup>
      )}

      {showSuccess && (
        <NotificationGroup style={{ right: 10, top: 60 }}>
          <Notification
            className="fade-notification"
            type={{ style: "success", icon: true }}
            closable={true}
            onClose={() => {
              setShowSuccess(false);
              onCloseSuccess?.();
            }}
          >
            <span>{successMessage}</span>
          </Notification>
        </NotificationGroup>
      )}

      <Card className="dashboard-header">
        <div className="header-content">
          <div className="breadcrumbs">
            {customBreadcrumb ? (
              customBreadcrumb
            ) : isDashboard ? (
              <h3>{t("dashboard")}</h3>
            ) : (
              breadcrumbKeys.map((key, index) => (
                <div key={key} className="breadcrumb-item">
                  {index !== 0 && <span className="breadcrumb-divider">/</span>}
                  <span>{t(`${key}`)}</span>
                </div>
              ))
            )}
          </div>
          <div className="header-actions">{headerActions}</div>
        </div>
      </Card>

      <Card className="dashboard-content-card">
        <div className="dashboard-main">{children}</div>
        {footer && <div className="dashboard-footer">{footer}</div>}
      </Card>
    </div>
  );
}
