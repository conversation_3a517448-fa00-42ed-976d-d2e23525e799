import { useState, useEffect, useMemo } from "react";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import type { TabStripSelectEventArguments } from "@progress/kendo-react-layout";
import { useGetClientsGridColumnsQuery, useGetClientListQuery } from "@/api/clientsApiSlice";
import type { TypeGridColumn } from "@/types/column";
import type { TypeClient } from "@/types/clients";
import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";

export function useClientsController() {
  // --- Assign logic ---
  const [activeTab, setActiveTab] = useState<number>(0);
  const [showAlert, setShowAlert] = useState<boolean>(true);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const handleTabSelect = (e: TabStripSelectEventArguments) => {
    setActiveTab(e.selected);
  };

  const handleTemplateChange = (e: DropDownListChangeEvent) => {
    setSelectedTemplate(e.value.id);
  };

  const handleClearSelection = (): void => {
    setSelectedTemplate(null);
  };

  const handleAlertClose = (): void => {
    setShowAlert(false);
  };

  const handleFooterCancel = (): void => {};
  const handleFooterConfirm = (): void => {};

  // --- Grid columns logic ---
  const { data: columnsData, isLoading: isColumnsLoading } = useGetClientsGridColumnsQuery();
  const [columns, setColumns] = useState<TypeGridColumn[]>([]);

  useEffect(() => {
    if (columnsData && !isColumnsLoading) {
      setColumns(columnsData.filter((item) => item.key !== "actions"));
    }
  }, [columnsData, isColumnsLoading]);

  // --- Clients list logic ---
  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const { data: clientListData, isLoading, refetch, isFetching } = useGetClientListQuery({
    skip,
    take,
    filters,
    sorts,
  });

  const clientList: TypeClient[] = useMemo(() => clientListData?.records || [], [clientListData]);
  const totalRecordCount: number = useMemo(() => clientListData?.totalRecordCount || 0, [clientListData]);

  return {
    // Assign
    activeTab,
    showAlert,
    selectedTemplate,
    handleTabSelect,
    handleTemplateChange,
    handleClearSelection,
    handleAlertClose,
    handleFooterCancel,
    handleFooterConfirm,
    // Grid columns
    columns,
    isColumnsLoading,
    // Clients list
    clientList,
    totalRecordCount,
    isLoading,
    refetch,
    isFetching,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    filters,
    sorts,
    pagination: { skip, take },
  };
} 