import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";
import { useState } from "react";
import { useTemplateGridColumns } from "./useTemplateGridColumns";
import { useTemplateList } from "./useTemplateList";

export const useTemplatesController = () => {
  const [selectedTemplateId, setSelectedTemplateId] = useState<
    string | undefined
  >(undefined);

  const { columns, isColumnsLoading } = useTemplateGridColumns();

  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const templateListParams = { skip, take, filters, sorts };

  const {
    templateListData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useTemplateList({ templateListParams });

  return {
    selectedTemplateId,
    setSelectedTemplateId,
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    columns,
    isColumnsLoading,
    templateListData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
    pagination: { skip, take },
  };
};
