import { Button } from "@progress/kendo-react-buttons";
import { Card } from "@progress/kendo-react-layout";

type ErrorFallbackProps = {
  error: Error;
  resetErrorBoundary: () => void;
};

const ErrorFallback = ({ error, resetErrorBoundary }: ErrorFallbackProps) => {
  return (
    <Card style={{ padding: "2rem", maxWidth: 600, margin: "2rem auto" }}>
      <h2>Something went wrong</h2>
      <pre>{error.message}</pre>
      <Button themeColor="primary" onClick={resetErrorBoundary}>
        Retry
      </Button>
    </Card>
  );
};

export default ErrorFallback;
